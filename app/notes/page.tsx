"use client"

import { useState, useEffect, useCallback } from "react"
import { FileText } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { useNotes } from '@/hooks/useNotes'
import { useTags } from '@/hooks/useTags'
import { SearchAndFilter } from '@/components/notes/SearchAndFilter'
import { NoteComposer } from '@/components/notes/NoteComposer'
import { NotesList } from '@/components/notes/NotesList'
import { DeleteNoteDialog } from '@/components/notes/DeleteNoteDialog'
import { Note } from '@/types/notes'

export default function NotesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredNotes, setFilteredNotes] = useState<Note[]>([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [user, setUser] = useState<any>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [noteToDelete, setNoteToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  const { notes, loading: notesLoading, createNote, deleteNote } = useNotes()
  const { tags, loading: tagsLoading, refetch: refetchTags } = useTags()

  // Проверка аутентификации
  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/login')
      } else {
        setUser(user)
      }
    }
    checkUser()
  }, [router, supabase])

  const filterNotes = useCallback(() => {
    if (searchQuery.trim() === "" && selectedTags.length === 0) {
      setFilteredNotes(notes)
    } else {
      const filtered = notes.filter((note) => {
        const matchesSearch = searchQuery.trim() === "" ||
          note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (note.summary_ai && note.summary_ai.toLowerCase().includes(searchQuery.toLowerCase()))

        const matchesTags = selectedTags.length === 0 ||
          note.tags.some((tag) => selectedTags.includes(tag))

        return matchesSearch && matchesTags
      })
      setFilteredNotes(filtered)
    }
  }, [notes, searchQuery, selectedTags])

  // Обновление отфильтрованных заметок при изменении данных
  useEffect(() => {
    filterNotes()
  }, [filterNotes])

  const handleSearchChange = (query: string) => {
    setSearchQuery(query)
  }

  const handleTagAdd = (tag: string) => {
    if (!selectedTags.includes(tag)) {
      setSelectedTags([...selectedTags, tag])
    }
  }

  const handleTagRemove = (tag: string) => {
    setSelectedTags(selectedTags.filter((t) => t !== tag))
  }

  const handleNoteSubmit = async (content: string) => {
    const newNote = await createNote({ content, content_type: 'text' })
    if (newNote) {
      await refetchTags()
    }
  }

  const handleDeleteClick = (noteId: string) => {
    setNoteToDelete(noteId)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (!noteToDelete) return

    setIsDeleting(true)
    try {
      const success = await deleteNote(noteToDelete)
      if (success) {
        setDeleteDialogOpen(false)
        setNoteToDelete(null)
        await refetchTags()
      }
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
    setNoteToDelete(null)
  }

  if (!user || notesLoading || tagsLoading) {
    return <div className="min-h-screen flex items-center justify-center">Загрузка...</div>
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Заголовок */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container max-w-4xl mx-auto px-4 flex h-14 items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-6 w-6" />
            <h1 className="text-lg font-semibold">Заметки</h1>
          </div>
          <form action="/auth/signout" method="post">
            <Button type="submit" variant="ghost" size="sm">
              Выйти
            </Button>
          </form>
        </div>
      </header>

      <div className="container max-w-4xl mx-auto p-4 space-y-4">
        <SearchAndFilter
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          selectedTags={selectedTags}
          onTagAdd={handleTagAdd}
          onTagRemove={handleTagRemove}
          availableTags={tags}
        />

        <NoteComposer
          onSubmit={handleNoteSubmit}
          userEmail={user.email}
        />

        <NotesList
          notes={filteredNotes}
          onTagClick={handleTagAdd}
          onDeleteClick={handleDeleteClick}
          userEmail={user.email}
        />

        <DeleteNoteDialog
          open={deleteDialogOpen}
          onOpenChange={handleDeleteCancel}
          onConfirm={handleDeleteConfirm}
          isDeleting={isDeleting}
        />
      </div>
    </div>
  )
}
